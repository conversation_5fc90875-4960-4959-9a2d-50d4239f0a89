<?php
/**
 * Arquivo de teste para verificar se o email aparece como texto normal
 * Execute este arquivo para testar o template de email
 */

// Simular ambiente WordPress básico
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

// Simular funções WordPress básicas
if (!function_exists('esc_html')) {
    function esc_html($text) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('esc_attr')) {
    function esc_attr($text) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('wp_kses_post')) {
    function wp_kses_post($text) {
        return $text;
    }
}

if (!function_exists('get_option')) {
    function get_option($option, $default = '') {
        $options = [
            'cnue_email_header_color' => '#4a90e2',
            'cnue_email_header_pattern' => 'none',
            'cnue_email_button_color' => '#4a90e2',
            'cnue_email_footer_text' => 'Este é um email automático, não responda.',
            'cnue_email_greeting' => 'Olá',
            'cnue_email_main_text' => 'Sua conta foi criada com sucesso em {site_name}! Abaixo estão seus dados de acesso:',
            'cnue_email_instruction_text' => 'Clique no botão abaixo para fazer login e começar a usar sua conta:',
            'cnue_email_button_text' => 'Fazer Login',
            'cnue_email_credentials_title' => 'Seus dados de acesso:',
            'cnue_email_username_label' => 'Usuário:',
            'cnue_email_password_label' => 'Senha:',
            'cnue_email_login_url_label' => 'Link de acesso:',
            'cnue_email_security_note' => 'Por segurança, recomendamos que você altere sua senha após o primeiro login.',
            'cnue_email_from_name' => 'ICU Educação'
        ];
        
        return isset($options[$option]) ? $options[$option] : $default;
    }
}

if (!function_exists('get_bloginfo')) {
    function get_bloginfo($show) {
        if ($show === 'name') {
            return 'ICU Educação';
        }
        return '';
    }
}

// Simular a classe CustomNewUserEmail para teste
if (!class_exists('CustomNewUserEmail')) {
    class CustomNewUserEmail {
        public static function make_email_non_clickable($email) {
            // Técnica 1: Quebrar com spans individuais
            $parts = explode('@', $email);
            if (count($parts) == 2) {
                $username = htmlspecialchars($parts[0], ENT_QUOTES, 'UTF-8');
                $domain = htmlspecialchars($parts[1], ENT_QUOTES, 'UTF-8');
                
                // Quebrar ainda mais o domínio
                $domain_parts = explode('.', $domain);
                $domain_display = implode('<span>.</span>', array_map('htmlspecialchars', $domain_parts));
                
                return '<span style="color: inherit !important; text-decoration: none !important; pointer-events: none !important; cursor: text !important;">' . 
                       '<span>' . $username . '</span>' .
                       '<span>@</span>' .
                       '<span>' . $domain_display . '</span>' .
                       '</span>';
            }
            
            // Fallback para emails malformados
            return '<span style="color: inherit !important; text-decoration: none !important; pointer-events: none !important; cursor: text !important;">' . htmlspecialchars($email, ENT_QUOTES, 'UTF-8') . '</span>';
        }
    }
}

// Criar usuário de teste com o email real do exemplo
$user = (object) [
    'display_name' => 'Sdadd',
    'user_email' => '<EMAIL>',
    'user_login' => 'sdadd',
    'user_pass' => '4!m#Vvwu#XoG^ER@ATFwSIC@',
    'first_name' => 'Sdadd'
];

$login_url = 'https://cursos.icueducacao.com/login';

echo "<h2>Teste do Template de Email - Email NÃO Clicável</h2>";
echo "<p><strong>Email de teste:</strong> " . $user->user_email . "</p>";
echo "<p><strong>Resultado esperado:</strong> O email deve aparecer como texto normal, sem ser clicável</p>";
echo "<hr>";

// Incluir o template
include 'templates/email-template.php';
?>