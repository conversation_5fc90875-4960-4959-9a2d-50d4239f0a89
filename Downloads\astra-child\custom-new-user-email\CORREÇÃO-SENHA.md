# 🔐 CORREÇÃO DA SENHA - Plugin Custom New User Email

## 🚨 PROBLEMA IDENTIFICADO:
O plugin estava mostrando a **senha criptografada/hash** do banco de dados ao invés da **senha em texto claro** que foi digitada.

**ANTES**: `$wp$2y$10$avpkb2FtGexN2Ow4juSZZ.oogGKB/q0iIfQ4ZvLa3/0z02/PEGZm`
**DEPOIS**: `minhasenha123` (senha real digitada)

## ✅ SOLUÇÃO IMPLEMENTADA:

### 1. **Captura da Senha Antes da Criptografia**
- Adicionado hook `user_register` com prioridade 1 (executa primeiro)
- Captura a senha de múltiplas fontes: `$_POST['pass1']`, `$_POST['user_pass']`, `$_POST['password']`
- Armazena temporariamente usando `transient` (expira em 5 minutos)

### 2. **Captura de Senhas Geradas Automaticamente**
- Intercepta o filtro `random_password` para senhas auto-geradas
- Detecta contexto de criação de usuário usando `debug_backtrace`
- Armazena em variável global temporária

### 3. **Recuperação no Template**
- Recupera a senha em texto claro do `transient`
- Substitui a senha criptografada pela senha real
- Remove o `transient` após uso para segurança

## 🔧 ARQUIVOS MODIFICADOS:

### `custom-new-user-email.php`:
```php
// Novos hooks adicionados:
add_action('user_register', [$this, 'capture_plain_password'], 1, 1);
add_filter('random_password', [$this, 'capture_generated_password'], 10, 1);

// Novas funções:
- capture_plain_password($user_id)
- capture_generated_password($password)
- Modificada: custom_new_user_notification_email()
```

## 🧪 COMO TESTAR:

### Teste 1: Senha Digitada Manualmente
```
1. Vá para: Usuários > Adicionar Novo
2. Digite nome de usuário e email
3. Digite uma senha específica (ex: "minhasenha123")
4. Clique em "Adicionar Novo Usuário"
5. Verifique o email recebido
6. RESULTADO ESPERADO: Deve mostrar "minhasenha123"
```

### Teste 2: Senha Gerada Automaticamente
```
1. Vá para: Usuários > Adicionar Novo
2. Digite nome de usuário e email
3. Deixe o campo senha vazio (WordPress gera automaticamente)
4. Clique em "Adicionar Novo Usuário"
5. Verifique o email recebido
6. RESULTADO ESPERADO: Deve mostrar a senha gerada pelo WordPress
```

### Teste 3: Verificar Logs
```
Procure nos logs (/wp-content/debug.log) por:
- "CNUE: Senha capturada de pass1"
- "CNUE: Senha gerada automaticamente capturada"
- "CNUE: Senha em texto claro recuperada"
```

## 🔍 LOGS DE MONITORAMENTO:

### ✅ Logs de Sucesso:
```
CNUE: Senha capturada de pass1
CNUE: Senha capturada e armazenada para usuário ID: 123
CNUE: Senha em texto claro recuperada para usuário: teste
```

### ⚠️ Logs de Atenção:
```
CNUE: Senha gerada automaticamente capturada: abc***
CNUE: Senha capturada de variável global
```

### ❌ Logs de Problema:
```
CNUE: Não foi possível capturar a senha para usuário ID: 123
CNUE: Senha em texto claro não encontrada para usuário: teste
```

## 🛡️ SEGURANÇA:

### Medidas de Segurança Implementadas:
1. **Transient com Expiração**: Senha armazenada por apenas 5 minutos
2. **Limpeza Automática**: Transient removido após uso
3. **Logs Mascarados**: Senhas nos logs mostram apenas 3 primeiros caracteres
4. **Verificação de Contexto**: Só captura senhas durante criação de usuário

### Dados Temporários:
- **Transient**: `cnue_plain_password_{user_id}` (expira em 300 segundos)
- **Global**: `$GLOBALS['cnue_temp_password']` (limpa automaticamente)

## 🎯 RESULTADO ESPERADO:

**ANTES**: 
```
Senha: $wp$2y$10$avpkb2FtGexN2Ow4juSZZ.oogGKB/q0iIfQ4ZvLa3/0z02/PEGZm
```

**DEPOIS**:
```
Senha: minhasenha123
```

## 🔄 FLUXO DE FUNCIONAMENTO:

1. **Usuário criado** → Hook `user_register` (prioridade 1)
2. **Senha capturada** → Armazenada em transient
3. **Email processado** → Hook `wp_new_user_notification_email`
4. **Senha recuperada** → Do transient para o template
5. **Email enviado** → Com senha em texto claro
6. **Limpeza** → Transient removido

A correção garante que o usuário receba a senha real que pode usar para fazer login!
